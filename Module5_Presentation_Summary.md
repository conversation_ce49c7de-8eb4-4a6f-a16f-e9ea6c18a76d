# Module 5: Statistical Analysis Presentation Summary
## Business Analyst Assignment - Employee Data Analysis

---

## **Slide 1: Title and Introduction**
**Module 5: Milestone 2 - Employee Data Statistical Analysis**
*Business Analyst: [Your Name]*
*Date: [Current Date]*
*Alpha Level: 0.05 for all statistical tests*

---

## **Slide 2: Gender and Education Analysis (Requirement 1.b)**
**Bar Graph: Average Salary by Gender for Employees with >12 Years Education**

- **Males with >12 years education**: n=173, Mean=$47,501, SE=$1,598
- **Females with >12 years education**: n=58, Mean=$32,508, SE=$1,314
- **Key Finding**: Males with higher education earn significantly more than females
- **Visual**: Bar chart with error bars showing standard error

---

## **Slide 3: Gender Salary Comparison (Requirement 1.c)**
**Hypothesis Test: Salary Differences for ≤12 Years Education**

**Test Results:**
- **Males (≤12 years)**: n=85, Mean=$29,109
- **Females (≤12 years)**: n=158, Mean=$23,655
- **H₀**: μ_male = μ_female (no difference)
- **H₁**: μ_male ≠ μ_female (difference exists)
- **t-statistic**: 8.3439
- **p-value**: <0.0001
- **Result**: **REJECT H₀** - Significant difference exists
- **Conclusion**: Males earn significantly more than females even with lower education levels

---

## **Slide 4: Minority Status Salary Analysis (Requirement 1.e)**
**Bar Graph: Average Salary by Minority Status**

- **Non-minority (0)**: n=370, Mean=$36,023, SE=$938
- **Minority (1)**: n=104, Mean=$28,714, SE=$1,120
- **Key Finding**: Non-minority employees earn significantly more
- **Visual**: Bar chart with error bars showing standard error

---

## **Slide 5: Minority Salary Hypothesis Test (Requirement 1.f)**
**Hypothesis Test: Salary Differences by Minority Status**

**Test Results:**
- **H₀**: μ_non-minority = μ_minority (no difference)
- **H₁**: μ_non-minority ≠ μ_minority (difference exists)
- **t-statistic**: 3.9148
- **p-value**: 0.0001
- **Result**: **REJECT H₀** - Significant difference exists
- **Conclusion**: Non-minority employees earn significantly higher salaries

---

## **Slide 6: Minority Education Analysis**
**Hypothesis Test: Education Differences by Minority Status**

**Test Results:**
- **Non-minority education**: Mean=13.69 years
- **Minority education**: Mean=12.77 years
- **H₀**: μ_non-minority_educ = μ_minority_educ (no difference)
- **H₁**: μ_non-minority_educ ≠ μ_minority_educ (difference exists)
- **t-statistic**: 2.9129
- **p-value**: 0.0038
- **Result**: **REJECT H₀** - Significant difference exists
- **Conclusion**: Non-minority employees have significantly more education

---

## **Slide 7: Female Job Tenure Analysis (Requirement 2.a)**
**Table: Females with ≥70 Months Job Time**

| Metric | Value |
|--------|-------|
| **Count** | 175 employees |
| **Mean Salary Gain** | $13,238 |
| **Standard Deviation** | $5,674 |
| **Minimum Gain** | $5,550 |
| **Maximum Gain** | $40,125 |

---

## **Slide 8: Female Salary Gains Test (Requirement 2.b)**
**Hypothesis Test: Female Salary Gains > $13,000**

**Test Results:**
- **Sample size**: 175
- **Sample mean**: $13,238
- **H₀**: μ ≤ $13,000
- **H₁**: μ > $13,000 (one-tailed test)
- **t-statistic**: 0.5555
- **p-value**: 0.2896
- **Result**: **FAIL TO REJECT H₀**
- **Conclusion**: Female salary gains are NOT significantly greater than $13,000

---

## **Slide 9: Male Job Tenure Analysis (Requirement 2.c)**
**Table: Males with ≥70 Months Job Time**

| Metric | Value |
|--------|-------|
| **Count** | 209 employees |
| **Mean Salary Gain** | $21,090 |
| **Standard Deviation** | $12,778 |
| **Minimum Gain** | $5,550 |
| **Maximum Gain** | $76,240 |

---

## **Slide 10: Male Salary Gains Test (Requirement 2.d)**
**Hypothesis Test: Male Salary Gains > $18,000**

**Test Results:**
- **Sample size**: 209
- **Sample mean**: $21,090
- **H₀**: μ ≤ $18,000
- **H₁**: μ > $18,000 (one-tailed test)
- **t-statistic**: 3.4960
- **p-value**: 0.0003
- **Result**: **REJECT H₀**
- **Conclusion**: Male salary gains ARE significantly greater than $18,000

---

## **Slide 11: Education Groups Analysis (Requirement 3.b)**
**Bar Graph: Average Salary by Education Groups**

| Education Group | Count | Mean Salary | Standard Error |
|----------------|-------|-------------|----------------|
| **≤12 years** | 243 | $25,563 | $353 |
| **13-16 years** | 181 | $37,075 | $1,063 |
| **>16 years** | 50 | $67,853 | $2,678 |

**Key Finding**: Clear salary progression with education level

---

## **Slide 12: Education Groups ANOVA Test (Requirement 3.c)**
**Hypothesis Test: Salary Differences Between Education Groups**

**Test Results:**
- **H₀**: μ₁ = μ₂ = μ₃ (no differences between groups)
- **H₁**: At least one group mean is different
- **F-statistic**: 290.9930
- **p-value**: <0.0001
- **Result**: **REJECT H₀**
- **Conclusion**: There ARE significant differences in salary between education groups

---

## **Slide 13: Summary of All Hypothesis Tests**

### **Tests Conducted and Results:**

1. **Gender-Education Salary Test**: ✅ **SIGNIFICANT** - Males earn more than females (≤12 years education)
2. **Minority Salary Test**: ✅ **SIGNIFICANT** - Non-minorities earn more than minorities
3. **Minority Education Test**: ✅ **SIGNIFICANT** - Non-minorities have more education
4. **Female Tenure Gains Test**: ❌ **NOT SIGNIFICANT** - Gains not significantly > $13,000
5. **Male Tenure Gains Test**: ✅ **SIGNIFICANT** - Gains significantly > $18,000
6. **Education Groups ANOVA**: ✅ **SIGNIFICANT** - Education level affects salary

### **Key Business Insights:**
- **Gender pay gap exists** across education levels
- **Minority status affects** both salary and education opportunities
- **Education strongly correlates** with salary progression
- **Male employees** show higher salary gains over tenure
- **Systemic inequalities** may exist requiring HR attention

---

## **Technical Notes:**
- All tests used α = 0.05 significance level
- Two-tailed tests used except for tenure gain tests (one-tailed)
- Standard errors calculated for all bar graphs
- Sample sizes adequate for statistical power
- Data from 474 employees across multiple variables
