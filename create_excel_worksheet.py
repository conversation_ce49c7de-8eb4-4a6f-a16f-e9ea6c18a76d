#!/usr/bin/env python3
"""
Create Excel worksheet with all statistical calculations for Module 5
"""

import pandas as pd
import numpy as np
from scipy.stats import ttest_ind, ttest_1samp, f_oneway
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

def create_excel_worksheet():
    """Create comprehensive Excel worksheet with all calculations"""
    
    # Load the data
    df = pd.read_excel('module5.xlsx', sheet_name='Sheet2')
    
    # Create Excel workbook
    wb = openpyxl.Workbook()
    
    # Remove default sheet
    wb.remove(wb.active)
    
    # Create sheets for each analysis
    create_summary_sheet(wb, df)
    create_gender_education_sheet(wb, df)
    create_minority_analysis_sheet(wb, df)
    create_tenure_analysis_sheet(wb, df)
    create_education_groups_sheet(wb, df)
    
    # Save the workbook
    wb.save('Module5_Statistical_Worksheet.xlsx')
    print("✓ Excel worksheet created: Module5_Statistical_Worksheet.xlsx")

def create_summary_sheet(wb, df):
    """Create summary sheet with data overview"""
    ws = wb.create_sheet("1. Data Summary")
    
    # Title
    ws['A1'] = "MODULE 5 STATISTICAL ANALYSIS - DATA SUMMARY"
    ws['A1'].font = Font(bold=True, size=14)
    
    # Basic statistics
    ws['A3'] = "Dataset Overview:"
    ws['A3'].font = Font(bold=True)
    ws['A4'] = f"Total Employees: {len(df)}"
    ws['A5'] = f"Males: {len(df[df['gender'] == 'm'])}"
    ws['A6'] = f"Females: {len(df[df['gender'] == 'f'])}"
    ws['A7'] = f"Non-minority: {len(df[df['minority'] == 0])}"
    ws['A8'] = f"Minority: {len(df[df['minority'] == 1])}"
    
    # Variable descriptions
    ws['A10'] = "Variable Descriptions:"
    ws['A10'].font = Font(bold=True)
    descriptions = [
        ("id", "Employee ID"),
        ("gender", "Gender (m/f)"),
        ("bdate", "Birth date"),
        ("educ", "Years of education"),
        ("jobcat", "Job category"),
        ("salary", "Current salary"),
        ("salbegin", "Beginning salary"),
        ("jobtime", "Months of job time"),
        ("prevexp", "Previous experience"),
        ("minority", "Minority status (0/1)")
    ]
    
    for i, (var, desc) in enumerate(descriptions):
        ws[f'A{11+i}'] = var
        ws[f'B{11+i}'] = desc

def create_gender_education_sheet(wb, df):
    """Create gender and education analysis sheet"""
    ws = wb.create_sheet("2. Gender-Education")
    
    # Title
    ws['A1'] = "GENDER AND EDUCATION ANALYSIS"
    ws['A1'].font = Font(bold=True, size=14)
    
    # 1.b Analysis
    ws['A3'] = "1.b: Males vs Females with >12 years education"
    ws['A3'].font = Font(bold=True)
    
    high_educ = df[df['educ'] > 12]
    male_high = high_educ[high_educ['gender'] == 'm']['salary']
    female_high = high_educ[high_educ['gender'] == 'f']['salary']
    
    ws['A5'] = "Group"
    ws['B5'] = "Count"
    ws['C5'] = "Mean Salary"
    ws['D5'] = "Std Dev"
    ws['E5'] = "Std Error"
    
    # Make header bold
    for col in ['A5', 'B5', 'C5', 'D5', 'E5']:
        ws[col].font = Font(bold=True)
    
    ws['A6'] = "Males (>12 years)"
    ws['B6'] = len(male_high)
    ws['C6'] = round(male_high.mean(), 2)
    ws['D6'] = round(male_high.std(), 2)
    ws['E6'] = round(male_high.std() / np.sqrt(len(male_high)), 2)
    
    ws['A7'] = "Females (>12 years)"
    ws['B7'] = len(female_high)
    ws['C7'] = round(female_high.mean(), 2)
    ws['D7'] = round(female_high.std(), 2)
    ws['E7'] = round(female_high.std() / np.sqrt(len(female_high)), 2)
    
    # 1.c Analysis
    ws['A10'] = "1.c: Hypothesis Test - Males vs Females (≤12 years education)"
    ws['A10'].font = Font(bold=True)
    
    low_educ = df[df['educ'] <= 12]
    male_low = low_educ[low_educ['gender'] == 'm']['salary']
    female_low = low_educ[low_educ['gender'] == 'f']['salary']
    
    t_stat, p_value = ttest_ind(male_low, female_low)
    
    ws['A12'] = "Males (≤12 years):"
    ws['B12'] = f"n={len(male_low)}, Mean=${male_low.mean():.2f}"
    ws['A13'] = "Females (≤12 years):"
    ws['B13'] = f"n={len(female_low)}, Mean=${female_low.mean():.2f}"
    ws['A14'] = "t-statistic:"
    ws['B14'] = round(t_stat, 4)
    ws['A15'] = "p-value:"
    ws['B15'] = f"{p_value:.6f}"
    ws['A16'] = "Result:"
    ws['B16'] = "REJECT H0" if p_value < 0.05 else "FAIL TO REJECT H0"

def create_minority_analysis_sheet(wb, df):
    """Create minority status analysis sheet"""
    ws = wb.create_sheet("3. Minority Analysis")
    
    # Title
    ws['A1'] = "MINORITY STATUS ANALYSIS"
    ws['A1'].font = Font(bold=True, size=14)
    
    # Salary analysis
    ws['A3'] = "1.e: Salary by Minority Status"
    ws['A3'].font = Font(bold=True)
    
    minority_0 = df[df['minority'] == 0]['salary']
    minority_1 = df[df['minority'] == 1]['salary']
    
    ws['A5'] = "Group"
    ws['B5'] = "Count"
    ws['C5'] = "Mean Salary"
    ws['D5'] = "Std Error"
    
    for col in ['A5', 'B5', 'C5', 'D5']:
        ws[col].font = Font(bold=True)
    
    ws['A6'] = "Non-minority (0)"
    ws['B6'] = len(minority_0)
    ws['C6'] = round(minority_0.mean(), 2)
    ws['D6'] = round(minority_0.std() / np.sqrt(len(minority_0)), 2)
    
    ws['A7'] = "Minority (1)"
    ws['B7'] = len(minority_1)
    ws['C7'] = round(minority_1.mean(), 2)
    ws['D7'] = round(minority_1.std() / np.sqrt(len(minority_1)), 2)
    
    # Hypothesis tests
    t_stat_sal, p_val_sal = ttest_ind(minority_0, minority_1)
    
    ws['A10'] = "1.f: Salary Hypothesis Test"
    ws['A10'].font = Font(bold=True)
    ws['A11'] = "t-statistic:"
    ws['B11'] = round(t_stat_sal, 4)
    ws['A12'] = "p-value:"
    ws['B12'] = f"{p_val_sal:.6f}"
    ws['A13'] = "Result:"
    ws['B13'] = "REJECT H0" if p_val_sal < 0.05 else "FAIL TO REJECT H0"
    
    # Education analysis
    minority_0_educ = df[df['minority'] == 0]['educ']
    minority_1_educ = df[df['minority'] == 1]['educ']
    t_stat_educ, p_val_educ = ttest_ind(minority_0_educ, minority_1_educ)
    
    ws['A16'] = "Education Hypothesis Test"
    ws['A16'].font = Font(bold=True)
    ws['A17'] = "Non-minority education:"
    ws['B17'] = f"{minority_0_educ.mean():.2f} years"
    ws['A18'] = "Minority education:"
    ws['B18'] = f"{minority_1_educ.mean():.2f} years"
    ws['A19'] = "t-statistic:"
    ws['B19'] = round(t_stat_educ, 4)
    ws['A20'] = "p-value:"
    ws['B20'] = f"{p_val_educ:.6f}"
    ws['A21'] = "Result:"
    ws['B21'] = "REJECT H0" if p_val_educ < 0.05 else "FAIL TO REJECT H0"

def create_tenure_analysis_sheet(wb, df):
    """Create job tenure analysis sheet"""
    ws = wb.create_sheet("4. Job Tenure")
    
    # Title
    ws['A1'] = "JOB TENURE ANALYSIS"
    ws['A1'].font = Font(bold=True, size=14)
    
    # Calculate salary gains
    df['salary_gain'] = df['salary'] - df['salbegin']
    
    # Female analysis
    ws['A3'] = "2.a: Females with ≥70 months job time"
    ws['A3'].font = Font(bold=True)
    
    females_70plus = df[(df['gender'] == 'f') & (df['jobtime'] >= 70)]
    
    ws['A5'] = "Count:"
    ws['B5'] = len(females_70plus)
    ws['A6'] = "Mean Salary Gain:"
    ws['B6'] = round(females_70plus['salary_gain'].mean(), 2)
    ws['A7'] = "Std Deviation:"
    ws['B7'] = round(females_70plus['salary_gain'].std(), 2)
    
    # Female hypothesis test
    ws['A10'] = "2.b: Female Salary Gains > $13,000"
    ws['A10'].font = Font(bold=True)
    
    t_stat_f, p_val_f_two = ttest_1samp(females_70plus['salary_gain'], 13000)
    p_val_f = p_val_f_two / 2  # One-tailed
    
    ws['A11'] = "t-statistic:"
    ws['B11'] = round(t_stat_f, 4)
    ws['A12'] = "p-value (one-tailed):"
    ws['B12'] = f"{p_val_f:.6f}"
    ws['A13'] = "Result:"
    ws['B13'] = "REJECT H0" if (p_val_f < 0.05 and t_stat_f > 0) else "FAIL TO REJECT H0"
    
    # Male analysis
    ws['A16'] = "2.c: Males with ≥70 months job time"
    ws['A16'].font = Font(bold=True)
    
    males_70plus = df[(df['gender'] == 'm') & (df['jobtime'] >= 70)]
    
    ws['A18'] = "Count:"
    ws['B18'] = len(males_70plus)
    ws['A19'] = "Mean Salary Gain:"
    ws['B19'] = round(males_70plus['salary_gain'].mean(), 2)
    ws['A20'] = "Std Deviation:"
    ws['B20'] = round(males_70plus['salary_gain'].std(), 2)
    
    # Male hypothesis test
    ws['A23'] = "2.d: Male Salary Gains > $18,000"
    ws['A23'].font = Font(bold=True)
    
    t_stat_m, p_val_m_two = ttest_1samp(males_70plus['salary_gain'], 18000)
    p_val_m = p_val_m_two / 2  # One-tailed
    
    ws['A24'] = "t-statistic:"
    ws['B24'] = round(t_stat_m, 4)
    ws['A25'] = "p-value (one-tailed):"
    ws['B25'] = f"{p_val_m:.6f}"
    ws['A26'] = "Result:"
    ws['B26'] = "REJECT H0" if (p_val_m < 0.05 and t_stat_m > 0) else "FAIL TO REJECT H0"

def create_education_groups_sheet(wb, df):
    """Create education groups analysis sheet"""
    ws = wb.create_sheet("5. Education Groups")
    
    # Title
    ws['A1'] = "EDUCATION GROUPS ANALYSIS"
    ws['A1'].font = Font(bold=True, size=14)
    
    # Create education groups
    df['educ_group'] = pd.cut(df['educ'], 
                             bins=[0, 12, 16, float('inf')], 
                             labels=['≤12 years', '13-16 years', '>16 years'],
                             include_lowest=True)
    
    # Group statistics
    ws['A3'] = "3.b: Education Group Statistics"
    ws['A3'].font = Font(bold=True)
    
    group_stats = df.groupby('educ_group')['salary'].agg(['count', 'mean', 'std']).reset_index()
    group_stats['se'] = group_stats['std'] / np.sqrt(group_stats['count'])
    
    ws['A5'] = "Education Group"
    ws['B5'] = "Count"
    ws['C5'] = "Mean Salary"
    ws['D5'] = "Std Error"
    
    for col in ['A5', 'B5', 'C5', 'D5']:
        ws[col].font = Font(bold=True)
    
    for i, row in group_stats.iterrows():
        ws[f'A{6+i}'] = row['educ_group']
        ws[f'B{6+i}'] = row['count']
        ws[f'C{6+i}'] = round(row['mean'], 2)
        ws[f'D{6+i}'] = round(row['se'], 2)
    
    # ANOVA test
    ws['A10'] = "3.c: ANOVA Test Results"
    ws['A10'].font = Font(bold=True)
    
    group1 = df[df['educ_group'] == '≤12 years']['salary']
    group2 = df[df['educ_group'] == '13-16 years']['salary']
    group3 = df[df['educ_group'] == '>16 years']['salary']
    
    f_stat, p_val_anova = f_oneway(group1, group2, group3)
    
    ws['A11'] = "F-statistic:"
    ws['B11'] = round(f_stat, 4)
    ws['A12'] = "p-value:"
    ws['B12'] = f"{p_val_anova:.6f}"
    ws['A13'] = "Result:"
    ws['B13'] = "REJECT H0" if p_val_anova < 0.05 else "FAIL TO REJECT H0"

if __name__ == "__main__":
    create_excel_worksheet()
