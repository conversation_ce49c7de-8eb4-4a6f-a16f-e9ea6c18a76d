#!/usr/bin/env python3
"""
Module 5 Statistical Analysis
Business Analyst Assignment - Employee Data Analysis

This script performs comprehensive statistical analysis on employee data
including salary, education, and minority status analysis.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import ttest_ind, f_oneway
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('default')
sns.set_palette("husl")

def load_and_explore_data(filename):
    """Load the Excel file and explore the data structure"""
    print("="*60)
    print("MODULE 5 STATISTICAL ANALYSIS")
    print("="*60)

    # Load the data - check for multiple sheets
    try:
        # First, check what sheets are available
        excel_file = pd.ExcelFile(filename)
        print(f"Available sheets: {excel_file.sheet_names}")

        # Try to load each sheet to find employee data
        for sheet_name in excel_file.sheet_names:
            print(f"\n--- Checking sheet: {sheet_name} ---")
            df = pd.read_excel(filename, sheet_name=sheet_name)
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")

            # Check if this looks like employee data
            employee_keywords = ['salary', 'education', 'minority', 'gender', 'employee', 'educ', 'sal']
            has_employee_data = any(keyword in str(df.columns).lower() for keyword in employee_keywords)

            if has_employee_data:
                print(f"✓ Found employee data in sheet: {sheet_name}")
                print(f"Dataset shape: {df.shape}")
                print("\n" + "="*40)
                print("DATA OVERVIEW")
                print("="*40)

                # Display basic info
                print("\nColumn names and data types:")
                print(df.dtypes)

                print("\nFirst few rows:")
                print(df.head())

                print("\nBasic statistics:")
                print(df.describe())

                print("\nMissing values:")
                print(df.isnull().sum())

                # Check unique values for categorical variables
                categorical_cols = df.select_dtypes(include=['object']).columns
                if len(categorical_cols) > 0:
                    print("\nUnique values in categorical columns:")
                    for col in categorical_cols:
                        print(f"{col}: {df[col].unique()}")

                return df

        # If no employee data found, load the first sheet anyway
        print(f"\nNo employee data found. Loading first sheet: {excel_file.sheet_names[0]}")
        df = pd.read_excel(filename, sheet_name=excel_file.sheet_names[0])
        print(f"✓ Successfully loaded data from {filename}")
        print(f"Dataset shape: {df.shape}")
        print("\n" + "="*40)
        print("DATA OVERVIEW")
        print("="*40)

        # Display basic info
        print("\nColumn names and data types:")
        print(df.dtypes)

        print("\nFirst few rows:")
        print(df.head())

        return df

    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def gender_education_analysis(df):
    """Requirement 1.b and 1.c: Gender and Education Analysis"""
    print("\n" + "="*60)
    print("REQUIREMENT 1: GENDER AND EDUCATION ANALYSIS")
    print("="*60)

    # 1.b: Bar graph comparing males vs females with >12 years education
    print("\n1.b: Average salary for males vs females with >12 years education")

    # Filter data for >12 years education
    high_educ = df[df['educ'] > 12]

    # Calculate means and standard errors
    male_high_educ = high_educ[high_educ['gender'] == 'm']['salary']
    female_high_educ = high_educ[high_educ['gender'] == 'f']['salary']

    male_mean = male_high_educ.mean()
    female_mean = female_high_educ.mean()
    male_se = male_high_educ.std() / np.sqrt(len(male_high_educ))
    female_se = female_high_educ.std() / np.sqrt(len(female_high_educ))

    print(f"Males with >12 years education: n={len(male_high_educ)}, Mean=${male_mean:.2f}, SE=${male_se:.2f}")
    print(f"Females with >12 years education: n={len(female_high_educ)}, Mean=${female_mean:.2f}, SE=${female_se:.2f}")

    # Create bar graph
    plt.figure(figsize=(10, 6))
    categories = ['Males (>12 years edu)', 'Females (>12 years edu)']
    means = [male_mean, female_mean]
    errors = [male_se, female_se]

    bars = plt.bar(categories, means, yerr=errors, capsize=5, color=['lightblue', 'lightcoral'], alpha=0.7)
    plt.title('Average Salary by Gender for Employees with >12 Years Education')
    plt.ylabel('Average Salary ($)')
    plt.grid(axis='y', alpha=0.3)

    # Add value labels on bars
    for bar, mean in zip(bars, means):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 500,
                f'${mean:.0f}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig('requirement_1b_gender_education_high.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 1.c: Hypothesis test for males vs females with ≤12 years education
    print("\n1.c: Hypothesis test for salary differences (≤12 years education)")

    # Filter data for ≤12 years education
    low_educ = df[df['educ'] <= 12]

    male_low_educ = low_educ[low_educ['gender'] == 'm']['salary']
    female_low_educ = low_educ[low_educ['gender'] == 'f']['salary']

    print(f"Males with ≤12 years education: n={len(male_low_educ)}, Mean=${male_low_educ.mean():.2f}")
    print(f"Females with ≤12 years education: n={len(female_low_educ)}, Mean=${female_low_educ.mean():.2f}")

    # Perform independent t-test
    t_stat, p_value = ttest_ind(male_low_educ, female_low_educ)

    print(f"\nHypothesis Test Results:")
    print(f"H0: μ_male = μ_female (no difference in average salaries)")
    print(f"H1: μ_male ≠ μ_female (difference in average salaries)")
    print(f"t-statistic: {t_stat:.4f}")
    print(f"p-value: {p_value:.4f}")
    print(f"Alpha level: 0.05")

    if p_value < 0.05:
        print(f"Result: REJECT H0 (p < 0.05)")
        print(f"Conclusion: There IS a significant difference in average salaries between males and females with ≤12 years education")
    else:
        print(f"Result: FAIL TO REJECT H0 (p ≥ 0.05)")
        print(f"Conclusion: There is NO significant difference in average salaries between males and females with ≤12 years education")

    return {
        'high_educ_data': {'male_mean': male_mean, 'female_mean': female_mean, 'male_se': male_se, 'female_se': female_se},
        'low_educ_test': {'t_stat': t_stat, 'p_value': p_value, 'male_mean': male_low_educ.mean(), 'female_mean': female_low_educ.mean()}
    }

def minority_analysis(df):
    """Requirements 1.e, 1.f, and additional: Minority Status Analysis"""
    print("\n" + "="*60)
    print("MINORITY STATUS ANALYSIS")
    print("="*60)

    # 1.e: Bar graph of average salary by minority levels
    print("\n1.e: Average salary by minority status")

    minority_0 = df[df['minority'] == 0]['salary']
    minority_1 = df[df['minority'] == 1]['salary']

    minority_0_mean = minority_0.mean()
    minority_1_mean = minority_1.mean()
    minority_0_se = minority_0.std() / np.sqrt(len(minority_0))
    minority_1_se = minority_1.std() / np.sqrt(len(minority_1))

    print(f"Non-minority (0): n={len(minority_0)}, Mean=${minority_0_mean:.2f}, SE=${minority_0_se:.2f}")
    print(f"Minority (1): n={len(minority_1)}, Mean=${minority_1_mean:.2f}, SE=${minority_1_se:.2f}")

    # Create bar graph
    plt.figure(figsize=(10, 6))
    categories = ['Non-Minority (0)', 'Minority (1)']
    means = [minority_0_mean, minority_1_mean]
    errors = [minority_0_se, minority_1_se]

    bars = plt.bar(categories, means, yerr=errors, capsize=5, color=['lightgreen', 'orange'], alpha=0.7)
    plt.title('Average Salary by Minority Status')
    plt.ylabel('Average Salary ($)')
    plt.grid(axis='y', alpha=0.3)

    # Add value labels on bars
    for bar, mean in zip(bars, means):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 500,
                f'${mean:.0f}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig('requirement_1e_minority_salary.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 1.f: Hypothesis test for salary differences between minority levels
    print("\n1.f: Hypothesis test for salary differences by minority status")

    t_stat_sal, p_value_sal = ttest_ind(minority_0, minority_1)

    print(f"\nSalary Hypothesis Test Results:")
    print(f"H0: μ_non_minority = μ_minority (no difference in average salaries)")
    print(f"H1: μ_non_minority ≠ μ_minority (difference in average salaries)")
    print(f"t-statistic: {t_stat_sal:.4f}")
    print(f"p-value: {p_value_sal:.4f}")
    print(f"Alpha level: 0.05")

    if p_value_sal < 0.05:
        print(f"Result: REJECT H0 (p < 0.05)")
        print(f"Conclusion: There IS a significant difference in average salaries between minority groups")
    else:
        print(f"Result: FAIL TO REJECT H0 (p ≥ 0.05)")
        print(f"Conclusion: There is NO significant difference in average salaries between minority groups")

    # Additional: Hypothesis test for education differences between minority levels
    print("\nAdditional: Hypothesis test for education differences by minority status")

    minority_0_educ = df[df['minority'] == 0]['educ']
    minority_1_educ = df[df['minority'] == 1]['educ']

    print(f"Non-minority education: n={len(minority_0_educ)}, Mean={minority_0_educ.mean():.2f} years")
    print(f"Minority education: n={len(minority_1_educ)}, Mean={minority_1_educ.mean():.2f} years")

    t_stat_educ, p_value_educ = ttest_ind(minority_0_educ, minority_1_educ)

    print(f"\nEducation Hypothesis Test Results:")
    print(f"H0: μ_non_minority_educ = μ_minority_educ (no difference in average education)")
    print(f"H1: μ_non_minority_educ ≠ μ_minority_educ (difference in average education)")
    print(f"t-statistic: {t_stat_educ:.4f}")
    print(f"p-value: {p_value_educ:.4f}")
    print(f"Alpha level: 0.05")

    if p_value_educ < 0.05:
        print(f"Result: REJECT H0 (p < 0.05)")
        print(f"Conclusion: There IS a significant difference in average education between minority groups")
    else:
        print(f"Result: FAIL TO REJECT H0 (p ≥ 0.05)")
        print(f"Conclusion: There is NO significant difference in average education between minority groups")

    return {
        'salary_data': {'minority_0_mean': minority_0_mean, 'minority_1_mean': minority_1_mean,
                       'minority_0_se': minority_0_se, 'minority_1_se': minority_1_se},
        'salary_test': {'t_stat': t_stat_sal, 'p_value': p_value_sal},
        'education_test': {'t_stat': t_stat_educ, 'p_value': p_value_educ,
                          'minority_0_mean': minority_0_educ.mean(), 'minority_1_mean': minority_1_educ.mean()}
    }

def job_tenure_analysis(df):
    """Requirements 2.a, 2.b, 2.c, 2.d: Job Tenure Analysis"""
    print("\n" + "="*60)
    print("REQUIREMENT 2: JOB TENURE ANALYSIS")
    print("="*60)

    # Calculate salary gains (current salary - beginning salary)
    df['salary_gain'] = df['salary'] - df['salbegin']

    # 2.a: Table showing number of females with 70 or more months of job time
    print("\n2.a: Females with 70 or more months of job time")

    females_70plus = df[(df['gender'] == 'f') & (df['jobtime'] >= 70)]
    print(f"Number of females with ≥70 months job time: {len(females_70plus)}")
    print(f"Average salary gain for this group: ${females_70plus['salary_gain'].mean():.2f}")

    # Create summary table
    female_tenure_summary = pd.DataFrame({
        'Count': [len(females_70plus)],
        'Mean Salary Gain': [females_70plus['salary_gain'].mean()],
        'Std Dev': [females_70plus['salary_gain'].std()],
        'Min Gain': [females_70plus['salary_gain'].min()],
        'Max Gain': [females_70plus['salary_gain'].max()]
    })
    print("\nSummary Table for Females with ≥70 months job time:")
    print(female_tenure_summary)

    # 2.b: Hypothesis test for females with 70+ months - salary gain > $13,000
    print("\n2.b: Hypothesis test - Female salary gains > $13,000")

    female_gains = females_70plus['salary_gain']
    test_value = 13000

    # One-sample t-test (one-tailed)
    t_stat_f, p_value_f_two = stats.ttest_1samp(female_gains, test_value)
    p_value_f = p_value_f_two / 2  # One-tailed test

    print(f"Sample size: {len(female_gains)}")
    print(f"Sample mean: ${female_gains.mean():.2f}")
    print(f"Test value: ${test_value}")
    print(f"H0: μ ≤ $13,000")
    print(f"H1: μ > $13,000 (one-tailed test)")
    print(f"t-statistic: {t_stat_f:.4f}")
    print(f"p-value (one-tailed): {p_value_f:.4f}")
    print(f"Alpha level: 0.05")

    if p_value_f < 0.05 and t_stat_f > 0:
        print(f"Result: REJECT H0 (p < 0.05)")
        print(f"Conclusion: Average salary gains for females with ≥70 months IS significantly greater than $13,000")
    else:
        print(f"Result: FAIL TO REJECT H0 (p ≥ 0.05)")
        print(f"Conclusion: Average salary gains for females with ≥70 months is NOT significantly greater than $13,000")

    # 2.c: Table showing number of males with 70 or more months of job time
    print("\n2.c: Males with 70 or more months of job time")

    males_70plus = df[(df['gender'] == 'm') & (df['jobtime'] >= 70)]
    print(f"Number of males with ≥70 months job time: {len(males_70plus)}")
    print(f"Average salary gain for this group: ${males_70plus['salary_gain'].mean():.2f}")

    # Create summary table
    male_tenure_summary = pd.DataFrame({
        'Count': [len(males_70plus)],
        'Mean Salary Gain': [males_70plus['salary_gain'].mean()],
        'Std Dev': [males_70plus['salary_gain'].std()],
        'Min Gain': [males_70plus['salary_gain'].min()],
        'Max Gain': [males_70plus['salary_gain'].max()]
    })
    print("\nSummary Table for Males with ≥70 months job time:")
    print(male_tenure_summary)

    # 2.d: Hypothesis test for males with 70+ months - salary gain > $18,000
    print("\n2.d: Hypothesis test - Male salary gains > $18,000")

    male_gains = males_70plus['salary_gain']
    test_value_m = 18000

    # One-sample t-test (one-tailed)
    t_stat_m, p_value_m_two = stats.ttest_1samp(male_gains, test_value_m)
    p_value_m = p_value_m_two / 2  # One-tailed test

    print(f"Sample size: {len(male_gains)}")
    print(f"Sample mean: ${male_gains.mean():.2f}")
    print(f"Test value: ${test_value_m}")
    print(f"H0: μ ≤ $18,000")
    print(f"H1: μ > $18,000 (one-tailed test)")
    print(f"t-statistic: {t_stat_m:.4f}")
    print(f"p-value (one-tailed): {p_value_m:.4f}")
    print(f"Alpha level: 0.05")

    if p_value_m < 0.05 and t_stat_m > 0:
        print(f"Result: REJECT H0 (p < 0.05)")
        print(f"Conclusion: Average salary gains for males with ≥70 months IS significantly greater than $18,000")
    else:
        print(f"Result: FAIL TO REJECT H0 (p ≥ 0.05)")
        print(f"Conclusion: Average salary gains for males with ≥70 months is NOT significantly greater than $18,000")

    return {
        'female_data': {'count': len(females_70plus), 'mean_gain': female_gains.mean(), 'summary': female_tenure_summary},
        'female_test': {'t_stat': t_stat_f, 'p_value': p_value_f, 'test_value': test_value},
        'male_data': {'count': len(males_70plus), 'mean_gain': male_gains.mean(), 'summary': male_tenure_summary},
        'male_test': {'t_stat': t_stat_m, 'p_value': p_value_m, 'test_value': test_value_m}
    }

def education_groups_analysis(df):
    """Requirements 3.b, 3.c: Education Groups Analysis"""
    print("\n" + "="*60)
    print("REQUIREMENT 3: EDUCATION GROUPS ANALYSIS")
    print("="*60)

    # Create three education groups
    # Group 1: ≤12 years, Group 2: 13-16 years, Group 3: >16 years
    df['educ_group'] = pd.cut(df['educ'],
                             bins=[0, 12, 16, float('inf')],
                             labels=['≤12 years', '13-16 years', '>16 years'],
                             include_lowest=True)

    # 3.b: Bar graph showing average salary of three education groups
    print("\n3.b: Average salary by education groups")

    group_stats = df.groupby('educ_group')['salary'].agg(['count', 'mean', 'std']).reset_index()
    group_stats['se'] = group_stats['std'] / np.sqrt(group_stats['count'])

    print("Education Group Statistics:")
    print(group_stats)

    # Create bar graph
    plt.figure(figsize=(12, 6))
    categories = group_stats['educ_group']
    means = group_stats['mean']
    errors = group_stats['se']

    bars = plt.bar(categories, means, yerr=errors, capsize=5,
                   color=['lightblue', 'lightgreen', 'lightcoral'], alpha=0.7)
    plt.title('Average Salary by Education Groups')
    plt.ylabel('Average Salary ($)')
    plt.xlabel('Education Groups')
    plt.grid(axis='y', alpha=0.3)

    # Add value labels on bars
    for bar, mean in zip(bars, means):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 500,
                f'${mean:.0f}', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig('requirement_3b_education_groups.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3.c: ANOVA test for significant differences between groups
    print("\n3.c: ANOVA test for salary differences between education groups")

    group1 = df[df['educ_group'] == '≤12 years']['salary']
    group2 = df[df['educ_group'] == '13-16 years']['salary']
    group3 = df[df['educ_group'] == '>16 years']['salary']

    # Perform one-way ANOVA
    f_stat, p_value_anova = f_oneway(group1, group2, group3)

    print(f"Group 1 (≤12 years): n={len(group1)}, Mean=${group1.mean():.2f}")
    print(f"Group 2 (13-16 years): n={len(group2)}, Mean=${group2.mean():.2f}")
    print(f"Group 3 (>16 years): n={len(group3)}, Mean=${group3.mean():.2f}")

    print(f"\nANOVA Test Results:")
    print(f"H0: μ1 = μ2 = μ3 (no differences between group means)")
    print(f"H1: At least one group mean is different")
    print(f"F-statistic: {f_stat:.4f}")
    print(f"p-value: {p_value_anova:.4f}")
    print(f"Alpha level: 0.05")

    if p_value_anova < 0.05:
        print(f"Result: REJECT H0 (p < 0.05)")
        print(f"Conclusion: There ARE significant differences in average salary between education groups")
    else:
        print(f"Result: FAIL TO REJECT H0 (p ≥ 0.05)")
        print(f"Conclusion: There are NO significant differences in average salary between education groups")

    return {
        'group_stats': group_stats,
        'anova_test': {'f_stat': f_stat, 'p_value': p_value_anova},
        'group_means': {'group1': group1.mean(), 'group2': group2.mean(), 'group3': group3.mean()}
    }

def main():
    """Main analysis function"""
    # Load and explore the data
    df = load_and_explore_data('module5.xlsx')

    if df is not None:
        print("\n" + "="*40)
        print("DATA LOADED SUCCESSFULLY")
        print("="*40)

        # Store results for presentation
        results = {}

        # Perform all analyses
        results['gender_education'] = gender_education_analysis(df)
        results['minority'] = minority_analysis(df)
        results['job_tenure'] = job_tenure_analysis(df)
        results['education_groups'] = education_groups_analysis(df)

        print("\n" + "="*60)
        print("ANALYSIS COMPLETE - READY FOR PRESENTATION")
        print("="*60)

        return df, results
    else:
        print("Failed to load data. Please check the file path and format.")
        return None, None

if __name__ == "__main__":
    df, results = main()
